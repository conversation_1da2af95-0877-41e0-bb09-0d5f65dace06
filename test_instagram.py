#!/usr/bin/env python3
import yt_dlp
import os

def test_instagram_download():
    """Test Instagram download functionality"""
    
    # Test URL - یک پست عمومی اینستاگرام
    test_url = "https://www.instagram.com/p/C_example/"  # این را با یک لینک واقعی جایگزین کنید
    
    # Configure yt-dlp options
    ydl_opts = {
        'format': 'best',
        'outtmpl': 'test_downloads/%(id)s.%(ext)s',
        'quiet': False,  # برای دیدن جزئیات
        'no_warnings': False,
        'extract_flat': False,
        'writeinfojson': False,
        'writethumbnail': False,
    }
    
    # Create test downloads directory
    if not os.path.exists('test_downloads'):
        os.makedirs('test_downloads')
    
    try:
        print("🔍 Testing Instagram download...")
        print(f"URL: {test_url}")
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Extract info first
            print("📋 Extracting info...")
            info = ydl.extract_info(test_url, download=False)
            
            if info:
                print(f"✅ Info extracted successfully!")
                print(f"Title: {info.get('title', 'N/A')}")
                print(f"Uploader: {info.get('uploader', 'N/A')}")
                print(f"Duration: {info.get('duration', 'N/A')}")
                print(f"Format: {info.get('ext', 'N/A')}")
                
                # Now try to download
                print("⬇️ Starting download...")
                ydl.download([test_url])
                print("✅ Download completed successfully!")
                
            else:
                print("❌ Could not extract info")
                
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("🤖 Instagram Download Test")
    print("=" * 40)
    
    # نمایش نسخه yt-dlp
    print(f"yt-dlp version: {yt_dlp.version.__version__}")
    
    # درخواست URL از کاربر
    url = input("\n🔗 لطفا لینک پست اینستاگرام را وارد کنید: ").strip()
    
    if url:
        # Configure yt-dlp options
        ydl_opts = {
            'format': 'best',
            'outtmpl': 'test_downloads/%(id)s.%(ext)s',
            'quiet': False,
            'no_warnings': False,
            'extract_flat': False,
        }
        
        # Create test downloads directory
        if not os.path.exists('test_downloads'):
            os.makedirs('test_downloads')
        
        try:
            print(f"\n🔍 Testing download for: {url}")
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)
                
                if info:
                    print(f"\n✅ Success!")
                    print(f"Title: {info.get('title', 'N/A')}")
                    print(f"Uploader: {info.get('uploader', 'N/A')}")
                    print(f"Format: {info.get('ext', 'N/A')}")
                    
                    # List downloaded files
                    if os.path.exists('test_downloads'):
                        files = os.listdir('test_downloads')
                        if files:
                            print(f"\n📁 Downloaded files:")
                            for file in files:
                                file_path = os.path.join('test_downloads', file)
                                size = os.path.getsize(file_path)
                                print(f"  - {file} ({size} bytes)")
                else:
                    print("❌ Could not extract info")
                    
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")
    else:
        print("❌ No URL provided")
