# 🤖 ربات دانلودر اینستاگرام

این ربات تلگرام برای دانلود عکس‌ها و ویدیوهای اینستاگرام طراحی شده است.

## 🚀 نصب و راه‌اندازی

### 1. نصب وابستگی‌ها
```bash
pip install -r requirements.txt
```

### 2. تنظیم متغیرهای محیطی
فایل `.env` را ویرایش کنید:
```
TELEGRAM_BOT_TOKEN=YOUR_BOT_TOKEN_HERE
INSTAGRAM_USERNAME=your_instagram_username
INSTAGRAM_PASSWORD=your_instagram_password
```

### 3. اجرای ربات
```bash
python bot.py
```

## 📱 نحوه استفاده

1. ربات را در تلگرام استارت کنید: `/start`
2. لینک پست اینستاگرام را ارسال کنید
3. ربات به صورت خودکار فایل را دانلود و ارسال می‌کند

## 🔧 مشکلات رایج و راه‌حل‌ها

### ❌ خطای اتصال (Connection Refused)
**علت:** مسدودیت اینترنت یا فایروال

**راه‌حل:**
- بررسی اتصال اینترنت
- استفاده از VPN
- بررسی تنظیمات فایروال
- اطمینان از عدم مسدودیت دسترسی به اینستاگرام

### ❌ پست خصوصی یا در دسترس نیست
**علت:** پست خصوصی است یا حذف شده

**راه‌حل:**
- استفاده از لینک پست‌های عمومی
- اطمینان از صحت لینک

### ❌ فایل بزرگتر از حد مجاز
**علت:** محدودیت 50MB تلگرام

**راه‌حل:**
- استفاده از فایل‌های کوچکتر
- فشرده‌سازی فایل

## 🛠️ تست عملکرد

برای تست عملکرد ربات:
```bash
python simple_test.py
```

## 📋 ویژگی‌ها

- ✅ دانلود عکس‌های اینستاگرام
- ✅ دانلود ویدیوهای اینستاگرام
- ✅ دانلود ریل‌ها (Reels)
- ✅ دانلود IGTV
- ✅ پشتیبانی از پست‌های چندتایی (Carousel)
- ✅ مدیریت خطاهای مختلف
- ✅ رابط کاربری فارسی

## ⚠️ نکات مهم

1. **محدودیت‌های قانونی:** فقط از محتوای عمومی و با رعایت حقوق مالکیت معنوی استفاده کنید
2. **محدودیت فنی:** فایل‌های بزرگتر از 50MB قابل ارسال در تلگرام نیستند
3. **مسدودیت شبکه:** در صورت مسدودیت اینستاگرام، از VPN استفاده کنید

## 🔄 بروزرسانی

برای بروزرسانی yt-dlp:
```bash
pip install --upgrade yt-dlp
```

## 📞 پشتیبانی

در صورت بروز مشکل:
1. ابتدا فایل `simple_test.py` را اجرا کنید
2. لاگ‌های خطا را بررسی کنید
3. اتصال اینترنت و VPN را بررسی کنید

## 📝 تغییرات اخیر

- ✅ بهبود مدیریت خطاها
- ✅ افزودن هدرهای HTTP برای جلوگیری از مسدودیت
- ✅ بهبود تشخیص نوع فایل
- ✅ افزودن محدودیت حجم فایل
- ✅ بهبود پیام‌های خطا
