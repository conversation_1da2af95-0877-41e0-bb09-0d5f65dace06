#!/usr/bin/env python3
"""
تست مستقیم دانلود از اینستاگرام
"""

import requests
import re
import os

def test_instagram_direct(url):
    """Test direct Instagram download"""
    print(f"🔍 Testing URL: {url}")
    
    try:
        # Extract post ID from URL
        post_id_match = re.search(r'/p/([^/]+)/', url) or re.search(r'/reel/([^/]+)/', url)
        if not post_id_match:
            print("❌ Invalid URL format")
            return False
        
        post_id = post_id_match.group(1)
        print(f"📋 Post ID: {post_id}")
        
        # Try to get post info using Instagram's embed API
        embed_url = f"https://www.instagram.com/p/{post_id}/embed/"
        print(f"🔗 Embed URL: {embed_url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        session = requests.Session()
        session.headers.update(headers)
        
        print("⏳ Fetching embed page...")
        response = session.get(embed_url, timeout=30)
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print(f"📄 Content length: {len(content)} characters")
            
            # Look for media URLs in the embed page
            print("🔍 Searching for media URLs...")
            
            # Try to find video URL
            video_match = re.search(r'"video_url":"([^"]+)"', content)
            if video_match:
                video_url = video_match.group(1).replace('\\u0026', '&')
                print(f"🎥 Found video URL: {video_url[:100]}...")
                return True
            
            # Try to find image URL
            image_match = re.search(r'"display_url":"([^"]+)"', content)
            if image_match:
                image_url = image_match.group(1).replace('\\u0026', '&')
                print(f"📸 Found image URL: {image_url[:100]}...")
                return True
            
            # Try other patterns
            media_patterns = [
                r'"src":"([^"]*instagram[^"]*\.jpg[^"]*)"',
                r'"src":"([^"]*instagram[^"]*\.mp4[^"]*)"',
                r'src="([^"]*instagram[^"]*\.jpg[^"]*)"',
                r'src="([^"]*instagram[^"]*\.mp4[^"]*)"',
            ]
            
            for pattern in media_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    print(f"🔍 Found {len(matches)} media URLs with pattern")
                    for i, match in enumerate(matches[:3]):  # Show first 3
                        print(f"  {i+1}. {match[:100]}...")
                    return True
            
            print("❌ No media URLs found in embed page")
            
            # Save content for debugging
            with open('debug_embed.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print("💾 Saved embed content to debug_embed.html for analysis")
            
        else:
            print(f"❌ Failed to fetch embed page: {response.status_code}")
            
        return False
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_yt_dlp_info_only(url):
    """Test yt-dlp info extraction only"""
    print(f"\n🔍 Testing yt-dlp info extraction for: {url}")
    
    try:
        import yt_dlp
        
        ydl_opts = {
            'quiet': False,
            'no_warnings': False,
            'extract_flat': False,
            'http_headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            },
            'socket_timeout': 30,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            print("⏳ Extracting info with yt-dlp...")
            info = ydl.extract_info(url, download=False)
            
            if info:
                print("✅ yt-dlp info extraction successful!")
                print(f"Title: {info.get('title', 'N/A')}")
                print(f"Uploader: {info.get('uploader', 'N/A')}")
                print(f"Format: {info.get('ext', 'N/A')}")
                print(f"Duration: {info.get('duration', 'N/A')}")
                return True
            else:
                print("❌ yt-dlp could not extract info")
                return False
                
    except Exception as e:
        print(f"❌ yt-dlp Error: {str(e)}")
        return False

def main():
    print("🧪 Direct Instagram Download Test")
    print("=" * 50)
    
    # Get URL from user
    url = input("🔗 Enter Instagram post URL: ").strip()
    
    if not url:
        print("❌ No URL provided")
        return
    
    # Validate URL
    if not ('instagram.com/p/' in url or 'instagram.com/reel/' in url):
        print("❌ Invalid Instagram URL")
        return
    
    print(f"\n🎯 Testing URL: {url}")
    
    # Test 1: Direct method
    print("\n" + "="*30 + " Direct Method " + "="*30)
    direct_success = test_instagram_direct(url)
    
    # Test 2: yt-dlp method
    print("\n" + "="*30 + " yt-dlp Method " + "="*30)
    ytdlp_success = test_yt_dlp_info_only(url)
    
    # Results
    print("\n" + "="*50)
    print("📊 Test Results:")
    print(f"{'✅' if direct_success else '❌'} Direct method: {'SUCCESS' if direct_success else 'FAILED'}")
    print(f"{'✅' if ytdlp_success else '❌'} yt-dlp method: {'SUCCESS' if ytdlp_success else 'FAILED'}")
    
    if direct_success or ytdlp_success:
        print("\n🎉 At least one method works! Bot should be able to download.")
    else:
        print("\n❌ Both methods failed. Possible issues:")
        print("- Network connectivity problems")
        print("- Instagram blocking/restrictions")
        print("- Private or deleted post")
        print("- Need VPN or different network")

if __name__ == "__main__":
    main()
