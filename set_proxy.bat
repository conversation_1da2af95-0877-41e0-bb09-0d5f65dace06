@echo off
echo Setting proxy environment variables for VPN...

REM Common VPN proxy ports - adjust based on your VPN
set HTTP_PROXY=http://127.0.0.1:8080
set HTTPS_PROXY=http://127.0.0.1:8080
set http_proxy=http://127.0.0.1:8080
set https_proxy=http://127.0.0.1:8080

echo Proxy variables set. Now run:
echo python bot.py

REM Alternative ports to try:
REM set HTTP_PROXY=http://127.0.0.1:1080
REM set HTTP_PROXY=http://127.0.0.1:3128
REM set HTTP_PROXY=http://127.0.0.1:8888

pause
