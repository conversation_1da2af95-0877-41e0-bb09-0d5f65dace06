# 🔧 راهنمای حل مشکل ربات اینستاگرام

## 🚨 مشکل اصلی شناسایی شده

**خطای اتصال: WinError 10061 - Connection Refused**

این خطا نشان می‌دهد که:
- اتصال به سرورهای اینستاگرام مسدود شده است
- احتمالاً اینستاگرام در شبکه شما فیلتر است
- نیاز به استفاده از VPN یا پروکسی

## ✅ راه‌حل‌های پیشنهادی

### 1. استفاده از VPN (بهترین راه‌حل)

**مراحل:**
1. یک VPN معتبر نصب کنید (مثل Windscribe، ProtonVPN، یا ExpressVPN)
2. به سرور خارجی متصل شوید
3. مطمئن شوید اتصال برقرار است
4. ربات را دوباره اجرا کنید

**تست اتصال:**
```bash
ping instagram.com
```

### 2. تغییر DNS

**Windows:**
1. Control Panel → Network and Internet → Network Connections
2. روی اتصال شبکه راست کلیک → Properties
3. Internet Protocol Version 4 (TCP/IPv4) → Properties
4. Use the following DNS server addresses:
   - Primary: *******
   - Secondary: *******
5. OK و Restart

### 3. استفاده از پروکسی

در فایل `.env` اضافه کنید:
```
PROXY_URL=http://proxy-server:port
PROXY_USERNAME=username
PROXY_PASSWORD=password
```

### 4. تست شبکه

```bash
# تست اتصال به اینستاگرام
curl -I https://www.instagram.com

# تست اتصال به یوتیوب (برای مقایسه)
curl -I https://www.youtube.com
```

## 🔄 مراحل عملی حل مشکل

### مرحله 1: نصب و راه‌اندازی VPN
```bash
# بعد از نصب VPN و اتصال، تست کنید:
python -c "import requests; print(requests.get('https://httpbin.org/ip').json())"
```

### مرحله 2: تست مجدد ربات
```bash
# اجرای ربات
python bot.py

# تست در ترمینال جداگانه
python test_direct.py
```

### مرحله 3: بررسی لاگ‌ها
اگر هنوز کار نمی‌کند، لاگ‌های ربات را بررسی کنید.

## 🛠️ ابزارهای کمکی

### اسکریپت تست اتصال
```python
import requests
import socket

def test_connection():
    try:
        # Test DNS resolution
        socket.gethostbyname('instagram.com')
        print("✅ DNS resolution works")
        
        # Test HTTP connection
        response = requests.get('https://www.instagram.com', timeout=10)
        print(f"✅ HTTP connection works: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

test_connection()
```

## 📱 تست با ربات

بعد از حل مشکل اتصال:

1. **اجرای ربات:**
   ```bash
   python bot.py
   ```

2. **ارسال لینک تست:**
   - لینک پست عمومی اینستاگرام
   - مثال: `https://www.instagram.com/p/[POST_ID]/`

3. **انتظار پیام‌های موفقیت:**
   ```
   ✅ ویدیو با موفقیت دانلود شد
   👤 از: @username
   📅 تاریخ: 20231201
   ```

## 🔍 عیب‌یابی پیشرفته

### بررسی فایل debug_embed.html
اگر روش مستقیم کار نکرد، فایل `debug_embed.html` را بررسی کنید:
- آیا محتوای صفحه embed بارگذاری شده؟
- آیا پیام خطای خاصی وجود دارد؟

### بررسی تنظیمات فایروال
- Windows Defender Firewall
- آنتی‌ویروس
- تنظیمات شرکتی/سازمانی

### تست با لینک‌های مختلف
- پست‌های مختلف
- ریل‌ها
- صفحات مختلف

## 📞 در صورت ادامه مشکل

اگر پس از انجام مراحل بالا هنوز مشکل دارید:

1. **اطلاعات سیستم:**
   - نسخه ویندوز
   - نوع اتصال اینترنت
   - آیا در شبکه سازمانی هستید؟

2. **تست‌های اضافی:**
   ```bash
   # تست اتصال مستقیم
   telnet instagram.com 443
   
   # بررسی مسیر شبکه
   tracert instagram.com
   ```

3. **لاگ‌های دقیق:**
   - خروجی کامل ربات
   - پیام‌های خطا
   - نتیجه تست‌ها

## 🎯 خلاصه

**مشکل اصلی:** مسدودیت اتصال به اینستاگرام
**راه‌حل اصلی:** استفاده از VPN
**تست نهایی:** اجرای ربات و ارسال لینک اینستاگرام

---

**نکته مهم:** این ربات فقط برای استفاده شخصی و با رعایت حقوق مالکیت معنوی طراحی شده است.
