#!/usr/bin/env python3
"""
تست سریع ربات با VPN
"""

import requests
import yt_dlp
import os

def test_with_vpn():
    print("🔍 Quick VPN Test for Instagram Bot")
    print("=" * 50)
    
    # Test 1: Check IP
    try:
        response = requests.get('https://httpbin.org/ip', timeout=10)
        ip_info = response.json()
        print(f"✅ Your IP: {ip_info['origin']}")
    except Exception as e:
        print(f"❌ IP check failed: {e}")
        return False
    
    # Test 2: Test Instagram with requests
    print("\n🌐 Testing Instagram with requests...")
    try:
        session = requests.Session()
        session.trust_env = True  # Use system proxy
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        session.headers.update(headers)
        
        response = session.get('https://www.instagram.com', timeout=30)
        print(f"✅ Instagram requests: {response.status_code}")
        requests_success = True
    except Exception as e:
        print(f"❌ Instagram requests failed: {e}")
        requests_success = False
    
    # Test 3: Test yt-dlp with simple URL
    print("\n🎥 Testing yt-dlp with Instagram...")
    try:
        # Configure yt-dlp with proxy support
        ydl_opts = {
            'quiet': False,
            'no_warnings': False,
            'socket_timeout': 60,
            'retries': 3,
            'force_ipv4': True,
            'http_headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            }
        }
        
        # Check for proxy environment variables
        proxy_url = os.getenv('HTTP_PROXY') or os.getenv('http_proxy') or os.getenv('HTTPS_PROXY') or os.getenv('https_proxy')
        if proxy_url:
            ydl_opts['proxy'] = proxy_url
            print(f"Using proxy: {proxy_url}")
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Test with a simple Instagram post
            test_url = 'https://www.instagram.com/p/DDLrVumA5sA/'
            print(f"Testing URL: {test_url}")
            
            info = ydl.extract_info(test_url, download=False)
            if info:
                print("✅ yt-dlp extraction successful!")
                print(f"Title: {info.get('title', 'N/A')}")
                ytdlp_success = True
            else:
                print("❌ yt-dlp extraction failed")
                ytdlp_success = False
                
    except Exception as e:
        print(f"❌ yt-dlp failed: {e}")
        ytdlp_success = False
    
    # Results
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"{'✅' if requests_success else '❌'} Requests method: {'SUCCESS' if requests_success else 'FAILED'}")
    print(f"{'✅' if ytdlp_success else '❌'} yt-dlp method: {'SUCCESS' if ytdlp_success else 'FAILED'}")
    
    if requests_success or ytdlp_success:
        print("\n🎉 VPN is working! Bot should be able to download.")
        print("📱 Try sending an Instagram link to your Telegram bot now!")
        return True
    else:
        print("\n❌ VPN is not working properly for Instagram.")
        print("🔧 Try:")
        print("- Disconnect and reconnect VPN")
        print("- Try different VPN server")
        print("- Check VPN settings")
        return False

if __name__ == "__main__":
    test_with_vpn()
