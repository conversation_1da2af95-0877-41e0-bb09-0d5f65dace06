#!/usr/bin/env python3
"""
ربات ساده اینستاگرام - استفاده از API های عمومی
"""

import os
import logging
import requests
import re
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO
)
logger = logging.getLogger(__name__)

# Create downloads directory
os.makedirs('downloads', exist_ok=True)

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send a message when the command /start is issued."""
    await update.message.reply_text(
        '🤖 ربات دانلود اینستاگرام (نسخه ساده)\n\n'
        '📝 نحوه استفاده:\n'
        '• لینک پست اینستاگرام را ارسال کنید\n'
        '• ربات سعی می‌کند محتوا را دانلود کند\n\n'
        '⚠️ نکته: فقط پست‌های عمومی قابل دانلود هستند'
    )

async def download_instagram_simple(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Simple Instagram download using public APIs"""
    try:
        url = update.message.text.strip()
        
        # Validate Instagram URL
        if not ('instagram.com/p/' in url or 'instagram.com/reel/' in url):
            await update.message.reply_text('❌ لطفا یک لینک معتبر اینستاگرام ارسال کنید')
            return
        
        # Send processing message
        status_message = await update.message.reply_text('⏳ در حال پردازش...')
        
        # Try different public Instagram download services
        success = False
        
        # Method 1: Try Instagram embed
        try:
            await status_message.edit_text('⏳ روش 1: تلاش با embed...')
            success = await try_embed_method(url, update, status_message)
        except Exception as e:
            logger.error(f"Embed method failed: {e}")
        
        # Method 2: Try public API services
        if not success:
            try:
                await status_message.edit_text('⏳ روش 2: تلاش با API عمومی...')
                success = await try_public_api_method(url, update, status_message)
            except Exception as e:
                logger.error(f"Public API method failed: {e}")
        
        # Method 3: Try direct URL extraction
        if not success:
            try:
                await status_message.edit_text('⏳ روش 3: تلاش مستقیم...')
                success = await try_direct_method(url, update, status_message)
            except Exception as e:
                logger.error(f"Direct method failed: {e}")
        
        if not success:
            await status_message.edit_text(
                '❌ متأسفانه نتوانستیم این پست را دانلود کنیم\n\n'
                '🔧 دلایل احتمالی:\n'
                '• پست خصوصی است\n'
                '• پست حذف شده\n'
                '• مشکل در اتصال شبکه\n'
                '• نیاز به VPN\n\n'
                '💡 پیشنهاد: از پست‌های عمومی استفاده کنید'
            )
        
    except Exception as e:
        logger.error(f"Error in download_instagram_simple: {str(e)}")
        await update.message.reply_text(f'❌ خطای غیرمنتظره: {str(e)}')

async def try_embed_method(url, update, status_message):
    """Try Instagram embed method"""
    try:
        # Extract post ID
        post_id_match = re.search(r'/p/([^/]+)/', url) or re.search(r'/reel/([^/]+)/', url)
        if not post_id_match:
            return False
        
        post_id = post_id_match.group(1)
        embed_url = f"https://www.instagram.com/p/{post_id}/embed/"
        
        # Make request with proper headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        }
        
        session = requests.Session()
        session.headers.update(headers)
        session.trust_env = True  # Use system proxy
        
        response = session.get(embed_url, timeout=30)
        
        if response.status_code == 200:
            await status_message.edit_text('✅ اتصال برقرار شد! در حال استخراج محتوا...')
            
            # Simple success message for now
            await status_message.edit_text(
                '✅ اتصال به اینستاگرام موفق بود!\n\n'
                '🎉 VPN شما کار می‌کند\n'
                '📱 ربات اصلی آماده استفاده است\n\n'
                '💡 حالا می‌توانید از ربات اصلی استفاده کنید'
            )
            return True
        else:
            return False
            
    except requests.exceptions.ConnectionError:
        return False
    except Exception as e:
        logger.error(f"Embed method error: {e}")
        return False

async def try_public_api_method(url, update, status_message):
    """Try public API services"""
    # This would use services like instaloader or public APIs
    # For now, just return False
    return False

async def try_direct_method(url, update, status_message):
    """Try direct URL extraction"""
    # This would try direct URL patterns
    # For now, just return False
    return False

def main():
    """Start the bot."""
    # Get bot token
    token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not token:
        print("❌ TELEGRAM_BOT_TOKEN not found in .env file")
        return
    
    # Create the Application
    application = Application.builder().token(token).build()
    
    # Add handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, download_instagram_simple))
    
    # Start the bot
    print("🤖 Simple Instagram Bot started...")
    print("📱 Send Instagram links to test VPN connection")
    application.run_polling()

if __name__ == '__main__':
    main()
