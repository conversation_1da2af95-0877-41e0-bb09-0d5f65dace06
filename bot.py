import os
import logging
import yt_dlp
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, CallbackQueryHandler
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Configure yt-dlp options for Instagram
ydl_opts = {
    'format': 'best',
    'outtmpl': 'downloads/%(id)s.%(ext)s',
    'quiet': True,
    'no_warnings': True,
    'extract_flat': False,
    'writeinfojson': False,
    'writethumbnail': False,
    'writesubtitles': False,
    'writeautomaticsub': False,
    # Add headers to avoid blocking
    'http_headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-us,en;q=0.5',
        'Accept-Encoding': 'gzip,deflate',
        'Accept-Charset': 'ISO-8859-1,utf-8;q=0.7,*;q=0.7',
        'Connection': 'keep-alive',
    },
    # Add retry options
    'retries': 3,
    'fragment_retries': 3,
    'socket_timeout': 30,
    # Remove username/password as they may cause issues
    # 'username': os.getenv('INSTAGRAM_USERNAME'),
    # 'password': os.getenv('INSTAGRAM_PASSWORD'),
}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send a message when the command /start is issued."""
    keyboard = [
        [InlineKeyboardButton("📸 دانلود عکس", callback_data='photo')],
        [InlineKeyboardButton("🎥 دانلود ویدیو", callback_data='video')],
        [InlineKeyboardButton("❓ راهنما", callback_data='help')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_text(
        'سلام! به ربات دانلودر اینستاگرام خوش آمدید. 👋\n\n'
        'این ربات به شما کمک می‌کند تا:\n'
        '📸 عکس‌های اینستاگرام\n'
        '🎥 ویدیوهای اینستاگرام\n'
        'را به راحتی دانلود کنید.\n\n'
        'لطفا لینک پست اینستاگرام را ارسال کنید.',
        reply_markup=reply_markup
    )

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send a message when the command /help is issued."""
    await update.message.reply_text(
        '📖 راهنمای استفاده از ربات:\n\n'
        '1️⃣ لینک پست اینستاگرام را کپی کنید\n'
        '2️⃣ آن را در این چت ارسال کنید\n'
        '3️⃣ ربات به صورت خودکار نوع محتوا را تشخیص می‌دهد\n'
        '4️⃣ فایل مورد نظر برای شما ارسال می‌شود\n\n'
        '⚠️ نکات مهم:\n'
        '• لینک باید از پست‌های عمومی اینستاگرام باشد\n'
        '• برای دانلود ریل‌ها هم می‌توانید از همین ربات استفاده کنید\n'
        '• حجم فایل‌ها محدودیت تلگرام را رعایت می‌کند'
    )

async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle button presses."""
    query = update.callback_query
    await query.answer()
    
    if query.data == 'help':
        await help_command(update, context)
    elif query.data in ['photo', 'video']:
        await query.message.reply_text(
            'لطفا لینک پست اینستاگرام را ارسال کنید.\n'
            'ربات به صورت خودکار نوع محتوا را تشخیص می‌دهد.'
        )

async def download_instagram(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Download Instagram post and send it to user."""
    try:
        # Get the message text (Instagram URL)
        url = update.message.text.strip()
        
        # Check if it's a valid Instagram URL
        if not ('instagram.com/p/' in url or 'instagram.com/reel/' in url or 'instagram.com/tv/' in url or 'instagram.com/stories/' in url):
            await update.message.reply_text(
                '❌ لینک نامعتبر!\n'
                'لطفا یک لینک معتبر اینستاگرام ارسال کنید.\n'
                'مثال: https://www.instagram.com/p/...' 
                'یا https://www.instagram.com/reel/...' 
                'یا https://www.instagram.com/tv/...' 
                'یا https://www.instagram.com/stories/...' 
            )
            return

        # Send "downloading" message
        status_message = await update.message.reply_text('⏳ در حال دانلود...')

        try:
            # Create downloads directory if it doesn't exist
            if not os.path.exists('downloads'):
                os.makedirs('downloads')

            # Download the video/photo
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)

                if not info:
                    raise Exception("اطلاعات پست قابل دریافت نیست")

                # Handle different types of media (single, carousel, story, TV)
                entries = []
                if '_type' in info and info['_type'] == 'playlist':
                    entries.extend(info['entries'])
                else:
                    entries.append(info)

                files_sent = 0
                for entry in entries:
                    if not entry:
                        continue

                    entry_id = entry.get('id', 'unknown')
                    entry_ext = entry.get('ext', 'mp4')
                    file_path = f'downloads/{entry_id}.{entry_ext}'

                    # Try to find the file with different extensions if not found
                    if not os.path.exists(file_path):
                        for ext in ['mp4', 'jpg', 'jpeg', 'png', 'webp', 'mov', 'avi']:
                            alt_path = f'downloads/{entry_id}.{ext}'
                            if os.path.exists(alt_path):
                                file_path = alt_path
                                entry_ext = ext
                                break

                    if os.path.exists(file_path):
                        try:
                            file_size = os.path.getsize(file_path)
                            if file_size > 50 * 1024 * 1024:  # 50MB limit for Telegram
                                await update.message.reply_text(
                                    f'❌ فایل بزرگتر از حد مجاز تلگرام است ({file_size / (1024*1024):.1f} MB)'
                                )
                                os.remove(file_path)
                                continue

                            # Determine file type and send accordingly
                            if entry_ext.lower() in ['mp4', 'mov', 'avi', 'mkv', 'webm']:
                                with open(file_path, 'rb') as video_file:
                                    await update.message.reply_video(
                                        video=video_file,
                                        caption=f'✅ ویدیو با موفقیت دانلود شد\n'
                                              f'👤 از: @{entry.get("uploader", "نامشخص")}\n'
                                              f'📅 تاریخ: {entry.get("upload_date", "نامشخص")}'
                                    )
                            else:
                                with open(file_path, 'rb') as photo_file:
                                    await update.message.reply_photo(
                                        photo=photo_file,
                                        caption=f'✅ عکس با موفقیت دانلود شد\n'
                                              f'👤 از: @{entry.get("uploader", "نامشخص")}\n'
                                              f'📅 تاریخ: {entry.get("upload_date", "نامشخص")}'
                                    )

                            files_sent += 1
                            # Delete the downloaded file
                            os.remove(file_path)

                        except Exception as send_error:
                            logger.error(f"Error sending file {file_path}: {str(send_error)}")
                            if os.path.exists(file_path):
                                os.remove(file_path)
                            continue
                    else:
                        logger.warning(f"File not found: {file_path}")

                if files_sent == 0:
                    raise FileNotFoundError("هیچ فایلی برای ارسال یافت نشد")

            # Delete the status message
            await status_message.delete()

        except yt_dlp.utils.DownloadError as e:
            logger.error(f"Download Error (yt-dlp): {str(e)}")
            error_message = '❌ خطا در دانلود!\n'
            error_str = str(e).lower()

            if any(keyword in error_str for keyword in ['login', 'private', 'not available', 'unavailable', 'blocked']):
                error_message += '• این پست خصوصی است یا در دسترس نیست\n'
                error_message += '• ممکن است حساب کاربری مسدود شده باشد\n'
                error_message += '• لطفا از لینک پست‌های عمومی استفاده کنید'
            elif any(keyword in error_str for keyword in ['connection', 'network', 'refused', 'timeout', 'unreachable']):
                error_message += '🌐 مشکل در اتصال به اینترنت یا مسدودیت شبکه\n\n'
                error_message += '🔧 راه‌حل‌های پیشنهادی:\n'
                error_message += '• بررسی اتصال اینترنت\n'
                error_message += '• استفاده از VPN در صورت مسدودیت\n'
                error_message += '• بررسی تنظیمات فایروال\n'
                error_message += '• چند دقیقه بعد دوباره تلاش کنید'
            elif 'format' in error_str:
                error_message += '• فرمت فایل پشتیبانی نمی‌شود\n'
                error_message += '• لطفا با لینک دیگری تلاش کنید'
            else:
                error_message += f'• {str(e).split("ERROR:")[-1].strip()}\n'

            error_message += '\n💡 راهنمایی عمومی:\n'
            error_message += '• مطمئن شوید لینک معتبر است\n'
            error_message += '• از پست‌های عمومی استفاده کنید\n'
            error_message += '• در صورت تداوم مشکل، از VPN استفاده کنید'
            await update.message.reply_text(error_message)

        except FileNotFoundError as e:
            logger.error(f"File Error: {str(e)}")
            await update.message.reply_text(
                '❌ خطا در ذخیره/ارسال فایل!\n'
                'لطفا دوباره تلاش کنید.'
            )
        except Exception as e:
            logger.error(f"Unexpected Error: {str(e)}")
            await update.message.reply_text(
                '❌ متأسفانه در دانلود مشکلی پیش آمد!\n'
                'لطفا دوباره تلاش کنید.'
            )
        finally:
            if status_message:
                await status_message.delete()

    except Exception as e:
        logger.error(f"General Error: {str(e)}")
        await update.message.reply_text(
            '❌ متأسفانه در دانلود مشکلی پیش آمد!\n'
            'لطفا دوباره تلاش کنید.'
        )

def main():
    """Start the bot."""
    # Create the Application
    application = Application.builder().token(os.getenv('TELEGRAM_BOT_TOKEN')).build()

    # Add handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CallbackQueryHandler(button_handler))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, download_instagram))

    # Start the Bot
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    main() 