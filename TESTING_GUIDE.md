# 🧪 راهنمای تست ربات اینستاگرام

## ✅ وضعیت فعلی ربات

### کتابخانه‌های نصب شده:
- ✅ python-telegram-bot: 20.7
- ✅ yt-dlp: 2025.06.30 (آخرین نسخه)
- ✅ python-dotenv: 1.0.0

### فایل‌های موجود:
- ✅ `bot.py` - کد اصلی ربات
- ✅ `.env` - تنظیمات محیطی
- ✅ `requirements.txt` - وابستگی‌ها
- ✅ `README.md` - راهنمای استفاده

## 🚀 نحوه تست ربات

### 1. بررسی وضعیت ربات
ربات در حال اجرا است و آماده دریافت پیام‌ها می‌باشد.

### 2. تست با لینک اینستاگرام

#### مرحله 1: یافتن لینک مناسب
برای تست، یک لینک پست عمومی اینستاگرام پیدا کنید:
- از صفحه‌های معروف و عمومی
- پست‌هایی که حجم کمی دارند
- مثال: `https://www.instagram.com/p/[POST_ID]/`

#### مرحله 2: ارسال لینک به ربات
1. ربات را در تلگرام پیدا کنید
2. دستور `/start` را ارسال کنید
3. لینک اینستاگرام را ارسال کنید
4. منتظر پاسخ ربات باشید

### 3. پیام‌های احتمالی ربات

#### ✅ موفقیت‌آمیز:
```
✅ ویدیو با موفقیت دانلود شد
👤 از: @username
📅 تاریخ: 20231201
```

#### ❌ خطاهای احتمالی:

**خطای اتصال:**
```
🌐 مشکل در اتصال به اینترنت یا مسدودیت شبکه

🔧 راه‌حل‌های پیشنهادی:
• بررسی اتصال اینترنت
• استفاده از VPN در صورت مسدودیت
• بررسی تنظیمات فایروال
• چند دقیقه بعد دوباره تلاش کنید
```

**پست خصوصی:**
```
• این پست خصوصی است یا در دسترس نیست
• ممکن است حساب کاربری مسدود شده باشد
• لطفا از لینک پست‌های عمومی استفاده کنید
```

## 🔧 حل مشکلات رایج

### مشکل 1: عدم پاسخ ربات
**علت:** ربات اجرا نشده یا توکن اشتباه است
**راه‌حل:**
1. بررسی اجرای `python bot.py`
2. بررسی توکن در فایل `.env`

### مشکل 2: خطای اتصال
**علت:** مسدودیت اینستاگرام یا مشکل شبکه
**راه‌حل:**
1. استفاده از VPN
2. بررسی اتصال اینترنت
3. تست با لینک‌های مختلف

### مشکل 3: فایل دانلود نمی‌شود
**علت:** پست خصوصی یا حذف شده
**راه‌حل:**
1. استفاده از پست‌های عمومی
2. بررسی صحت لینک

## 📋 چک‌لیست تست

- [ ] ربات اجرا شده است (`python bot.py`)
- [ ] دستور `/start` کار می‌کند
- [ ] لینک معتبر اینستاگرام آماده است
- [ ] اتصال اینترنت برقرار است
- [ ] در صورت نیاز VPN فعال است

## 🎯 لینک‌های تست پیشنهادی

برای تست، از این نوع لینک‌ها استفاده کنید:
- پست‌های عمومی صفحات معروف
- ریل‌های کوتاه (کمتر از 1 دقیقه)
- عکس‌های تکی

**مثال فرمت لینک:**
```
https://www.instagram.com/p/ABC123def456/
https://www.instagram.com/reel/XYZ789ghi012/
```

## 📞 در صورت مشکل

اگر ربات کار نمی‌کند:
1. لاگ‌های کنسول را بررسی کنید
2. اتصال اینترنت را تست کنید
3. از VPN استفاده کنید
4. با لینک‌های مختلف تست کنید

## 🔄 بروزرسانی

برای بهترین عملکرد:
```bash
pip install --upgrade yt-dlp
```

---

**نکته مهم:** این ربات فقط برای استفاده شخصی و با رعایت حقوق مالکیت معنوی طراحی شده است.
