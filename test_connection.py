#!/usr/bin/env python3
"""
تست اتصال شبکه برای ربات اینستاگرام
"""

import requests
import socket
import time

def test_dns():
    """تست DNS resolution"""
    print("🔍 Testing DNS resolution...")
    
    sites = ['instagram.com', 'youtube.com', 'google.com']
    
    for site in sites:
        try:
            ip = socket.gethostbyname(site)
            print(f"✅ {site} → {ip}")
        except Exception as e:
            print(f"❌ {site} → {str(e)}")

def test_http_connection():
    """تست اتصال HTTP"""
    print("\n🌐 Testing HTTP connections...")
    
    sites = [
        'https://www.google.com',
        'https://www.youtube.com', 
        'https://www.instagram.com',
        'https://httpbin.org/ip'
    ]
    
    for site in sites:
        try:
            start_time = time.time()
            response = requests.get(site, timeout=10)
            end_time = time.time()
            
            print(f"✅ {site} → {response.status_code} ({end_time-start_time:.2f}s)")
            
        except requests.exceptions.ConnectionError as e:
            print(f"❌ {site} → Connection Error: {str(e)}")
        except requests.exceptions.Timeout:
            print(f"⏰ {site} → Timeout")
        except Exception as e:
            print(f"❌ {site} → {str(e)}")

def test_instagram_specific():
    """تست اختصاصی اینستاگرام"""
    print("\n📸 Testing Instagram specific endpoints...")
    
    endpoints = [
        'https://www.instagram.com',
        'https://www.instagram.com/robots.txt',
        'https://i.instagram.com',
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, headers=headers, timeout=15)
            print(f"✅ {endpoint} → {response.status_code}")
            
        except requests.exceptions.ConnectionError as e:
            if "10061" in str(e):
                print(f"🚫 {endpoint} → BLOCKED (Connection Refused)")
            else:
                print(f"❌ {endpoint} → Connection Error")
        except Exception as e:
            print(f"❌ {endpoint} → {str(e)}")

def check_proxy_settings():
    """بررسی تنظیمات پروکسی"""
    print("\n🔧 Checking proxy settings...")
    
    import os
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"🔗 {var}: {value}")
        else:
            print(f"➖ {var}: Not set")

def get_public_ip():
    """دریافت IP عمومی"""
    print("\n🌍 Getting public IP...")
    
    services = [
        'https://httpbin.org/ip',
        'https://api.ipify.org?format=json',
        'https://ipinfo.io/json'
    ]
    
    for service in services:
        try:
            response = requests.get(service, timeout=10)
            if response.status_code == 200:
                print(f"✅ Your IP: {response.text.strip()}")
                break
        except:
            continue
    else:
        print("❌ Could not determine public IP")

def main():
    print("🔍 Network Connection Test for Instagram Bot")
    print("=" * 60)
    
    # Test 1: DNS
    test_dns()
    
    # Test 2: HTTP connections
    test_http_connection()
    
    # Test 3: Instagram specific
    test_instagram_specific()
    
    # Test 4: Proxy settings
    check_proxy_settings()
    
    # Test 5: Public IP
    get_public_ip()
    
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print("If Instagram connections failed with 'Connection Refused' error:")
    print("  🔧 Solution: Use VPN to bypass network restrictions")
    print("  📱 Recommended: Connect to a foreign VPN server")
    print("  ⚡ Then restart the bot and try again")
    
    print("\nIf all connections work:")
    print("  ✅ Network is fine, bot should work normally")
    print("  🤖 Try running: python bot.py")

if __name__ == "__main__":
    main()
