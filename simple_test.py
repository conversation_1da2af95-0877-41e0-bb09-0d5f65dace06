#!/usr/bin/env python3
import yt_dlp
import os

def test_basic_functionality():
    """Test basic yt-dlp functionality"""
    
    print("🤖 Testing yt-dlp basic functionality")
    print("=" * 40)
    
    # نمایش نسخه
    print(f"yt-dlp version: {yt_dlp.version.__version__}")
    
    # تست با یک ویدیو ساده از YouTube (برای اطمینان از کارکرد yt-dlp)
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll - کوتاه و معروف
    
    ydl_opts = {
        'format': 'worst',  # کیفیت پایین برای تست سریع
        'outtmpl': 'test_downloads/%(id)s.%(ext)s',
        'quiet': False,
        'no_warnings': False,
        'extract_flat': False,
    }
    
    # Create test downloads directory
    if not os.path.exists('test_downloads'):
        os.makedirs('test_downloads')
    
    try:
        print(f"\n🔍 Testing with YouTube URL: {test_url}")
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # فقط اطلاعات را استخراج کن، دانلود نکن
            info = ydl.extract_info(test_url, download=False)
            
            if info:
                print(f"\n✅ yt-dlp is working!")
                print(f"Title: {info.get('title', 'N/A')}")
                print(f"Duration: {info.get('duration', 'N/A')} seconds")
                print(f"Uploader: {info.get('uploader', 'N/A')}")
                return True
            else:
                print("❌ Could not extract info")
                return False
                
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        return False

def test_instagram_info_only():
    """Test Instagram info extraction only (no download)"""
    
    print("\n" + "=" * 40)
    print("🔍 Testing Instagram info extraction")
    
    # درخواست URL از کاربر
    url = input("\n🔗 لطفا لینک پست اینستاگرام را وارد کنید (یا Enter برای رد کردن): ").strip()
    
    if not url:
        print("⏭️ Instagram test skipped")
        return True
    
    ydl_opts = {
        'quiet': False,
        'no_warnings': False,
        'extract_flat': False,
        'http_headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
        'socket_timeout': 30,
    }
    
    try:
        print(f"\n🔍 Testing Instagram URL: {url}")
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # فقط اطلاعات را استخراج کن
            info = ydl.extract_info(url, download=False)
            
            if info:
                print(f"\n✅ Instagram info extraction successful!")
                print(f"Title: {info.get('title', 'N/A')}")
                print(f"Uploader: {info.get('uploader', 'N/A')}")
                print(f"Format: {info.get('ext', 'N/A')}")
                print(f"Duration: {info.get('duration', 'N/A')}")
                return True
            else:
                print("❌ Could not extract Instagram info")
                return False
                
    except Exception as e:
        print(f"\n❌ Instagram Error: {str(e)}")
        return False

if __name__ == "__main__":
    # تست عملکرد پایه
    basic_ok = test_basic_functionality()
    
    if basic_ok:
        # تست اینستاگرام
        instagram_ok = test_instagram_info_only()
        
        print("\n" + "=" * 40)
        print("📊 Test Results:")
        print(f"✅ Basic yt-dlp: {'OK' if basic_ok else 'FAILED'}")
        print(f"{'✅' if instagram_ok else '❌'} Instagram: {'OK' if instagram_ok else 'FAILED'}")
        
        if basic_ok and instagram_ok:
            print("\n🎉 All tests passed! Bot should work correctly.")
        elif basic_ok:
            print("\n⚠️ yt-dlp works but Instagram may have issues.")
            print("This could be due to:")
            print("- Network restrictions")
            print("- Instagram blocking")
            print("- Invalid URL")
        else:
            print("\n❌ Basic functionality failed. Check your installation.")
    else:
        print("\n❌ Basic test failed. Please check your yt-dlp installation.")
