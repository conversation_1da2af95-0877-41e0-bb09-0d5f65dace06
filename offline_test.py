#!/usr/bin/env python3
"""
تست آفلاین ربات - بررسی عملکرد کد بدون نیاز به اتصال اینترنت
"""

import os
import sys
import importlib.util

def test_imports():
    """تست import کردن کتابخانه‌ها"""
    print("🔍 Testing imports...")
    
    try:
        import telegram
        print("✅ python-telegram-bot imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import telegram: {e}")
        return False
    
    try:
        import yt_dlp
        print(f"✅ yt-dlp imported successfully (version: {yt_dlp.version.__version__})")
    except ImportError as e:
        print(f"❌ Failed to import yt-dlp: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import dotenv: {e}")
        return False
    
    return True

def test_env_file():
    """تست فایل .env"""
    print("\n🔍 Testing .env file...")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        return False
    
    print("✅ .env file exists")
    
    # بررسی محتوای فایل
    try:
        with open('.env', 'r') as f:
            content = f.read()
            
        if 'TELEGRAM_BOT_TOKEN' in content:
            print("✅ TELEGRAM_BOT_TOKEN found in .env")
        else:
            print("⚠️ TELEGRAM_BOT_TOKEN not found in .env")
            
        if 'INSTAGRAM_USERNAME' in content:
            print("✅ INSTAGRAM_USERNAME found in .env")
        else:
            print("⚠️ INSTAGRAM_USERNAME not found in .env")
            
        return True
        
    except Exception as e:
        print(f"❌ Error reading .env: {e}")
        return False

def test_bot_code():
    """تست کد ربات"""
    print("\n🔍 Testing bot code...")
    
    if not os.path.exists('bot.py'):
        print("❌ bot.py not found")
        return False
    
    print("✅ bot.py exists")
    
    try:
        # بررسی syntax کد
        with open('bot.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'bot.py', 'exec')
        print("✅ bot.py syntax is valid")
        
        # بررسی وجود توابع اصلی
        if 'def start(' in code:
            print("✅ start function found")
        else:
            print("❌ start function not found")
            
        if 'def download_instagram(' in code:
            print("✅ download_instagram function found")
        else:
            print("❌ download_instagram function not found")
            
        if 'def main(' in code:
            print("✅ main function found")
        else:
            print("❌ main function not found")
            
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error in bot.py: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading bot.py: {e}")
        return False

def test_url_validation():
    """تست تابع validation URL"""
    print("\n🔍 Testing URL validation logic...")
    
    # URLs تست
    test_urls = [
        "https://www.instagram.com/p/ABC123/",
        "https://www.instagram.com/reel/XYZ789/",
        "https://www.instagram.com/tv/DEF456/",
        "https://www.instagram.com/stories/user/123/",
        "https://instagram.com/p/ABC123/",
        "https://www.youtube.com/watch?v=123",  # نامعتبر
        "not_a_url",  # نامعتبر
        "",  # خالی
    ]
    
    valid_count = 0
    for url in test_urls:
        is_valid = ('instagram.com/p/' in url or 
                   'instagram.com/reel/' in url or 
                   'instagram.com/tv/' in url or 
                   'instagram.com/stories/' in url)
        
        if url in ["https://www.instagram.com/p/ABC123/", 
                   "https://www.instagram.com/reel/XYZ789/",
                   "https://www.instagram.com/tv/DEF456/",
                   "https://www.instagram.com/stories/user/123/",
                   "https://instagram.com/p/ABC123/"]:
            if is_valid:
                print(f"✅ {url} - correctly identified as valid")
                valid_count += 1
            else:
                print(f"❌ {url} - incorrectly identified as invalid")
        else:
            if not is_valid:
                print(f"✅ {url} - correctly identified as invalid")
                valid_count += 1
            else:
                print(f"❌ {url} - incorrectly identified as valid")
    
    return valid_count == len(test_urls)

def test_directory_creation():
    """تست ایجاد پوشه downloads"""
    print("\n🔍 Testing directory creation...")
    
    test_dir = "test_downloads_temp"
    
    try:
        if not os.path.exists(test_dir):
            os.makedirs(test_dir)
            print(f"✅ Successfully created {test_dir}")
        else:
            print(f"✅ {test_dir} already exists")
        
        # پاک کردن پوشه تست
        if os.path.exists(test_dir):
            os.rmdir(test_dir)
            print(f"✅ Successfully removed {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error with directory operations: {e}")
        return False

def main():
    """اجرای تست‌های آفلاین"""
    print("🤖 Offline Bot Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Environment File Test", test_env_file),
        ("Bot Code Test", test_bot_code),
        ("URL Validation Test", test_url_validation),
        ("Directory Creation Test", test_directory_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "="*50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All offline tests passed!")
        print("\n💡 Next steps:")
        print("1. Make sure you have internet connection")
        print("2. Test with a real Instagram URL")
        print("3. Check if your network allows Instagram access")
        print("4. Consider using VPN if Instagram is blocked")
    else:
        print("⚠️ Some tests failed. Please fix the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
